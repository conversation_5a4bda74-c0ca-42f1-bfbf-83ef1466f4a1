-- =====================================================
-- 积分统计系统重构 - 数据库迁移脚本
-- 版本: v2.2
-- 创建时间: 2025-01-XX
-- 目的: 废弃 child_points 表，在 user_camp_participations 表中添加 total_points 字段
-- =====================================================

-- 1. 在 user_camp_participations 表中添加 total_points 字段
ALTER TABLE `user_camp_participations` 
ADD COLUMN `total_points` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '该孩子在此训练营的总积分' 
AFTER `last_checkin_date`;

-- 2. 为新字段添加索引（用于排行榜查询）
ALTER TABLE `user_camp_participations` 
ADD INDEX `idx_total_points` (`total_points` DESC) COMMENT '训练营积分排行索引';

-- 3. 数据迁移：将 child_points 表中的积分数据迁移到 user_camp_participations 表
-- 注意：只有当 child_points 表存在数据时才执行此步骤
UPDATE `user_camp_participations` ucp
INNER JOIN `child_points` cp ON ucp.id = cp.participation_id
SET ucp.total_points = cp.total_points
WHERE cp.participation_id > 0;

-- 4. 验证数据迁移结果
-- 检查迁移后的数据一致性
SELECT 
    COUNT(*) as total_participations,
    COUNT(CASE WHEN total_points > 0 THEN 1 END) as participations_with_points,
    SUM(total_points) as total_points_sum
FROM user_camp_participations;

-- 5. 备份 child_points 表（重命名为备份表）
-- 注意：在确认系统运行正常后，可以删除此备份表
RENAME TABLE `child_points` TO `child_points_backup_20250105`;

-- 6. 更新 children 表的全局积分统计
-- 重新计算每个孩子的全局积分（所有训练营积分之和）
UPDATE children c
SET total_points = (
    SELECT COALESCE(SUM(ucp.total_points), 0)
    FROM user_camp_participations ucp
    WHERE ucp.child_id = c.id
    AND ucp.deleted_at IS NULL
)
WHERE c.deleted_at IS NULL;

-- 7. 验证全局积分统计
SELECT 
    c.id as child_id,
    c.total_points as global_points,
    COALESCE(SUM(ucp.total_points), 0) as calculated_points
FROM children c
LEFT JOIN user_camp_participations ucp ON c.id = ucp.child_id AND ucp.deleted_at IS NULL
WHERE c.deleted_at IS NULL
GROUP BY c.id, c.total_points
HAVING c.total_points != calculated_points;

-- 如果上述查询有结果，说明数据不一致，需要重新执行步骤6

-- =====================================================
-- 回滚脚本（如果需要回滚）
-- =====================================================
/*
-- 回滚步骤1：恢复 child_points 表
RENAME TABLE `child_points_backup_20250105` TO `child_points`;

-- 回滚步骤2：删除新增的字段和索引
ALTER TABLE `user_camp_participations` DROP INDEX `idx_total_points`;
ALTER TABLE `user_camp_participations` DROP COLUMN `total_points`;
*/

-- =====================================================
-- 迁移完成检查清单
-- =====================================================
/*
1. ✅ user_camp_participations 表已添加 total_points 字段
2. ✅ 积分数据已从 child_points 迁移到 user_camp_participations
3. ✅ children 表的全局积分已重新计算
4. ✅ child_points 表已备份并重命名
5. ✅ 数据一致性验证通过
6. ⏳ 应用代码已更新并部署
7. ⏳ 功能测试通过
8. ⏳ 性能测试通过
*/
