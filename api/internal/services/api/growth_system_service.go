package api

import (
	"fmt"
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"
)

// GrowthSystemService 成长系统协调层服务接口
type GrowthSystemService interface {
	// 获取成长页面完整数据
	GetGrowthPageData(userID uint, childID uint) (*GrowthPageResponse, error)
	// 处理打卡后的系统更新
	HandleCheckinComplete(childID uint, campID uint, checkinID uint) error
	// 处理契约完成后的系统更新
	HandleContractComplete(childID uint, contractID uint) error
	// 获取用户成长统计
	GetGrowthStats(childID uint) (*GrowthStatsResponse, error)
}

// growthSystemService 成长系统协调层服务实现
type growthSystemService struct {
	trainingCampService       TrainingCampService
	checkinService            CheckinService
	pointsService             PointsService
	contractService           ContractService
	medalsRepo                repositories.MedalsRepository
	childMedalsRepo           repositories.ChildMedalsRepository
	growthTracksRepo          repositories.GrowthTracksRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
}

// NewGrowthSystemService 创建成长系统协调层服务
func NewGrowthSystemService(
	trainingCampService TrainingCampService,
	checkinService CheckinService,
	pointsService PointsService,
	contractService ContractService,
	medalsRepo repositories.MedalsRepository,
	childMedalsRepo repositories.ChildMedalsRepository,
	growthTracksRepo repositories.GrowthTracksRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
) GrowthSystemService {
	return &growthSystemService{
		trainingCampService:       trainingCampService,
		checkinService:            checkinService,
		pointsService:             pointsService,
		contractService:           contractService,
		medalsRepo:                medalsRepo,
		childMedalsRepo:           childMedalsRepo,
		growthTracksRepo:          growthTracksRepo,
		userCampParticipationRepo: userCampParticipationRepo,
	}
}

// ==================== 响应结构定义 ====================

// GrowthPageResponse 成长页面响应
type GrowthPageResponse struct {
	UserStats    *UserStatsInfo      `json:"user_stats"`    // 用户统计信息
	CampList     []*UserCampResponse `json:"camp_list"`     // 训练营列表
	ContractList []*ContractResponse `json:"contract_list"` // 契约列表
	GrowthTrack  []*GrowthTrackItem  `json:"growth_track"`  // 成长轨迹
	TodayStatus  *TodayStatusInfo    `json:"today_status"`  // 今日状态
	MedalsList   []*MedalInfo        `json:"medals_list"`   // 勋章列表
}

// UserStatsInfo 用户统计信息
type UserStatsInfo struct {
	TotalPoints        int64   `json:"total_points"`        // 总积分
	CurrentLevel       int     `json:"current_level"`       // 当前等级
	StreakDays         int     `json:"streak_days"`         // 连续打卡天数
	CompletedContracts int     `json:"completed_contracts"` // 完成契约数
	WeekRank           int     `json:"week_rank"`           // 本周排名
	CompletionRate     float64 `json:"completion_rate"`     // 完成率
}

// GrowthTrackItem 成长轨迹项目
type GrowthTrackItem struct {
	Type        string `json:"type"`        // 类型：checkin, contract, honor, award
	Title       string `json:"title"`       // 标题
	Description string `json:"description"` // 描述
	Date        string `json:"date"`        // 日期
	Points      int    `json:"points"`      // 获得积分
}

// TodayStatusInfo 今日状态信息
type TodayStatusInfo struct {
	HasCheckedIn     bool     `json:"has_checked_in"`    // 是否已打卡
	ActiveCamps      int      `json:"active_camps"`      // 活跃训练营数
	PendingContracts int      `json:"pending_contracts"` // 待完成契约数
	TodayPoints      int      `json:"today_points"`      // 今日积分
	Recommendations  []string `json:"recommendations"`   // 推荐行动
}

// MedalInfo 勋章信息
type MedalInfo struct {
	ID                   uint   `json:"id"`                    // 勋章ID
	Name                 string `json:"name"`                  // 勋章名称
	Description          string `json:"description"`           // 勋章描述
	Icon                 string `json:"icon"`                  // 勋章图标
	Level                int    `json:"level"`                 // 勋章等级 1:铜 2:银 3:金 4:钻石
	LevelName            string `json:"level_name"`            // 等级名称
	ConditionType        int    `json:"condition_type"`        // 条件类型 1:打卡次数 2:连续天数 3:积分达标
	ConditionDescription string `json:"condition_description"` // 条件描述
	PointsReward         int    `json:"points_reward"`         // 奖励积分
	IsUnlocked           bool   `json:"unlocked"`              // 是否已解锁
	Progress             int    `json:"progress"`              // 当前进度
	Target               int    `json:"target"`                // 目标值
	UnlockedAt           string `json:"unlocked_at"`           // 解锁时间
	IsContractMedal      bool   `json:"is_contract_medal"`     // 是否为契约勋章
	SortOrder            int    `json:"sort_order"`            // 排序权重
}

// GrowthStatsResponse 成长统计响应
type GrowthStatsResponse struct {
	OverallStats  *UserStatsInfo               `json:"overall_stats"`  // 总体统计
	CampStats     *CampStatsInfo               `json:"camp_stats"`     // 训练营统计
	CheckinStats  *models.CheckinStatsResponse `json:"checkin_stats"`  // 打卡统计
	ContractStats *ContractStatsResponse       `json:"contract_stats"` // 契约统计
	PointsStats   *PointsStatsResponse         `json:"points_stats"`   // 积分统计
}

// CampStatsInfo 训练营统计信息
type CampStatsInfo struct {
	TotalJoined     int     `json:"total_joined"`     // 总参与数
	ActiveCamps     int     `json:"active_camps"`     // 活跃训练营
	CompletedCamps  int     `json:"completed_camps"`  // 完成训练营
	AverageProgress float64 `json:"average_progress"` // 平均进度
}

// ==================== 服务方法实现 ====================

// GetGrowthPageData 获取成长页面完整数据
func (s *growthSystemService) GetGrowthPageData(userID uint, childID uint) (*GrowthPageResponse, error) {
	// 获取用户统计信息
	userStats, err := s.getUserStats(childID)
	if err != nil {
		logger.Error("Failed to get user stats", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取用户统计失败")
	}

	// 获取训练营列表
	var campList []*UserCampResponse
	if s.trainingCampService != nil {
		camps, err := s.trainingCampService.GetUserCamps(userID, childID)
		if err != nil {
			logger.Error("Failed to get user camps", "child_id", childID, "error", err)
			campList = []*UserCampResponse{} // 失败时返回空列表
		} else {
			campList = camps
		}
	} else {
		campList = []*UserCampResponse{} // 训练营服务未初始化时返回空列表
	}

	// 获取契约列表
	var contractList []*ContractResponse
	if s.contractService != nil {
		contracts, err := s.contractService.GetContracts(childID, 0) // 0表示获取所有状态的契约
		if err != nil {
			logger.Error("Failed to get contracts", "child_id", childID, "error", err)
			contractList = []*ContractResponse{} // 失败时返回空列表
		} else {
			contractList = contracts
		}
	} else {
		contractList = []*ContractResponse{} // 契约服务未初始化时返回空列表
	}

	// 获取成长轨迹
	growthTrack, err := s.getGrowthTrack(childID)
	if err != nil {
		logger.Error("Failed to get growth track", "child_id", childID, "error", err)
		growthTrack = []*GrowthTrackItem{} // 失败时返回空列表
	}

	// 获取今日状态
	todayStatus, err := s.getTodayStatus(childID, len(campList), len(contractList))
	if err != nil {
		logger.Error("Failed to get today status", "child_id", childID, "error", err)
		todayStatus = &TodayStatusInfo{} // 失败时返回空状态
	}

	// 获取勋章数据
	medalsList, err := s.getMedalsData(childID)
	if err != nil {
		logger.Error("Failed to get medals data", "child_id", childID, "error", err)
		medalsList = []*MedalInfo{} // 失败时返回空列表
	}

	response := &GrowthPageResponse{
		UserStats:    userStats,
		CampList:     campList,
		ContractList: contractList,
		GrowthTrack:  growthTrack,
		TodayStatus:  todayStatus,
		MedalsList:   medalsList,
	}

	logger.Info("Growth page data retrieved successfully", "child_id", childID)
	return response, nil
}

// HandleCheckinComplete 处理打卡后的系统更新
func (s *growthSystemService) HandleCheckinComplete(childID uint, campID uint, checkinID uint) error {
	// 这个方法在打卡完成后被调用，用于协调各个系统的更新

	// 1. 更新训练营进度（已在CheckinService中处理）

	// 2. 更新相关契约进度（暂时注释，后续版本实现）
	// 家庭契约功能暂时不实现
	/*
		if s.contractService != nil {
			contracts, err := s.contractService.GetContracts(childID, 1) // 获取进行中的契约
			if err != nil {
				logger.Error("Failed to get contracts for update", "child_id", childID, "error", err)
			} else {
				for _, contract := range contracts {
					if uint(contract.CampID) == campID {
						// 计算新的进度
						newProgress := float64(contract.CurrentProgress+1) / float64(contract.GoalValue) * 100
						if newProgress > 100 {
							newProgress = 100
						}

						if err := s.contractService.UpdateContractProgress(contract.ID, newProgress); err != nil {
							logger.Error("Failed to update contract progress", "contract_id", contract.ID, "error", err)
						}
					}
				}
			}
		}
	*/

	// 3. 记录成长轨迹（暂时注释，后续版本实现）
	// 成长轨迹功能暂时不实现
	/*
		err := s.recordGrowthTrack(childID, campID, checkinID)
		if err != nil {
			logger.Error("Failed to record growth track", "child_id", childID, "camp_id", campID, "checkin_id", checkinID, "error", err)
			// 成长轨迹记录失败不影响整体流程
		}
	*/

	// 4. 检查并解锁勋章（暂时注释，后续版本实现）
	// 勋章系统暂时不实现
	/*
		err = s.checkAndUnlockMedals(childID)
		if err != nil {
			logger.Error("Failed to check and unlock medals", "child_id", childID, "error", err)
			// 勋章检查失败不影响整体流程
		}
	*/

	logger.Info("Checkin complete handling finished", "child_id", childID, "camp_id", campID)
	return nil
}

// HandleContractComplete 处理契约完成后的系统更新
func (s *growthSystemService) HandleContractComplete(childID uint, contractID uint) error {
	// 这个方法在契约完成后被调用

	// 1. 积分奖励（已在ContractService中处理）

	// 2. 记录成长轨迹
	// TODO: 实现成长轨迹记录

	// 3. 检查是否解锁新成就
	// TODO: 实现成就系统

	logger.Info("Contract complete handling finished", "child_id", childID, "contract_id", contractID)
	return nil
}

// GetGrowthStats 获取用户成长统计
func (s *growthSystemService) GetGrowthStats(childID uint) (*GrowthStatsResponse, error) {
	// TODO: 重构为基于participation_id的统计
	// 暂时返回默认值以便编译通过
	pointsStats := &PointsStatsResponse{
		TotalPoints:    0,
		CurrentLevel:   1,
		ContinuousDays: 0,
		WeekRank:       0,
	}

	var checkinStats *models.CheckinStatsResponse
	if s.checkinService != nil {
		stats, err := s.checkinService.GetCheckinStatsAll(childID)
		if err != nil {
			logger.Error("Failed to get checkin stats", "child_id", childID, "error", err)
			return nil, errcode.ErrDatabase.WithDetails("获取打卡统计失败")
		}
		checkinStats = stats
	} else {
		// 如果打卡服务未初始化，返回默认值
		checkinStats = &models.CheckinStatsResponse{
			TotalCheckins:      0,
			ConsecutiveDays:    0,
			MaxConsecutiveDays: 0,
			TotalStudyMinutes:  0,
			AverageFeeling:     0.0,
			ThisWeekCheckins:   0,
			ThisMonthCheckins:  0,
		}
	}

	var contractStats *ContractStatsResponse
	// 家庭契约功能暂时不实现
	contractStats = &ContractStatsResponse{
		TotalContracts:     0,
		CompletedContracts: 0,
		ActiveContracts:    0,
		CompletionRate:     0.0,
	}
	/*
		if s.contractService != nil {
			stats, err := s.contractService.GetContractStats(childID)
			if err != nil {
				logger.Error("Failed to get contract stats", "child_id", childID, "error", err)
				return nil, errcode.ErrDatabase.WithDetails("获取契约统计失败")
			}
			contractStats = stats
		} else {
			// 如果契约服务未初始化，返回默认值
			contractStats = &ContractStatsResponse{
				TotalContracts:     0,
				CompletedContracts: 0,
				ActiveContracts:    0,
				CompletionRate:     0.0,
			}
		}
	*/

	// 计算训练营统计
	campStats := &CampStatsInfo{
		TotalJoined:     0, // TODO: 实现训练营统计
		ActiveCamps:     0,
		CompletedCamps:  0,
		AverageProgress: 0.0,
	}

	// 组合总体统计
	overallStats := &UserStatsInfo{
		TotalPoints:        pointsStats.TotalPoints,
		CurrentLevel:       pointsStats.CurrentLevel,
		StreakDays:         pointsStats.ContinuousDays,
		CompletedContracts: contractStats.CompletedContracts,
		WeekRank:           pointsStats.WeekRank,
		CompletionRate:     contractStats.CompletionRate,
	}

	response := &GrowthStatsResponse{
		OverallStats:  overallStats,
		CampStats:     campStats,
		CheckinStats:  checkinStats,
		ContractStats: contractStats,
		PointsStats:   pointsStats,
	}

	logger.Info("Growth stats retrieved successfully", "child_id", childID)
	return response, nil
}

// ==================== 辅助方法 ====================

// getUserStats 获取用户统计信息
func (s *growthSystemService) getUserStats(childID uint) (*UserStatsInfo, error) {
	// TODO: 重构为基于participation_id的统计
	// 暂时返回默认值以便编译通过
	pointsStats := &PointsStatsResponse{
		TotalPoints:    0,
		CurrentLevel:   1,
		ContinuousDays: 0,
		WeekRank:       0,
	}

	// 获取契约统计（暂时注释，后续版本实现）
	var contractStats *ContractStatsResponse
	// 家庭契约功能暂时不实现
	contractStats = &ContractStatsResponse{
		TotalContracts:     0,
		CompletedContracts: 0,
		ActiveContracts:    0,
		CompletionRate:     0.0,
	}
	/*
		if s.contractService != nil {
			stats, err := s.contractService.GetContractStats(childID)
			if err != nil {
				return nil, err
			}
			contractStats = stats
		} else {
			// 如果契约服务未初始化，返回默认值
			contractStats = &ContractStatsResponse{
				TotalContracts:     0,
				CompletedContracts: 0,
				ActiveContracts:    0,
				CompletionRate:     0.0,
			}
		}
	*/

	return &UserStatsInfo{
		TotalPoints:        pointsStats.TotalPoints,
		CurrentLevel:       pointsStats.CurrentLevel,
		StreakDays:         pointsStats.ContinuousDays,
		CompletedContracts: contractStats.CompletedContracts,
		WeekRank:           pointsStats.WeekRank,
		CompletionRate:     contractStats.CompletionRate,
	}, nil
}

// getGrowthTrack 获取成长轨迹
func (s *growthSystemService) getGrowthTrack(childID uint) ([]*GrowthTrackItem, error) {
	// 从growth_tracks表获取成长轨迹记录
	growthTracks, _, err := s.growthTracksRepo.GetByChildID(childID, 0, 20) // 获取最近20条记录
	if err != nil {
		logger.Error("Failed to get growth tracks from database", "child_id", childID, "error", err)
		return []*GrowthTrackItem{}, nil // 返回空列表而不是错误
	}

	var growthTrack []*GrowthTrackItem
	for _, track := range growthTracks {
		// 将growth_tracks模型转换为GrowthTrackItem
		item := &GrowthTrackItem{
			Type:        s.getMilestoneTypeString(track.MilestoneType),
			Title:       track.MilestoneTitle,
			Description: track.MilestoneDescription,
			Date:        track.CreatedAt.Format("2006-01-02"),
			Points:      s.parseAchievementPoints(track.AchievementValue),
		}
		growthTrack = append(growthTrack, item)
	}

	logger.Info("Growth track retrieved successfully", "child_id", childID, "count", len(growthTrack))
	return growthTrack, nil
}

// getMilestoneTypeString 将里程碑类型转换为字符串
func (s *growthSystemService) getMilestoneTypeString(milestoneType int8) string {
	switch milestoneType {
	case 1:
		return "checkin" // 首次打卡
	case 2:
		return "streak" // 连续打卡
	case 3:
		return "camp" // 完成训练营
	case 4:
		return "medal" // 获得勋章
	default:
		return "other"
	}
}

// parseAchievementPoints 解析成就积分
func (s *growthSystemService) parseAchievementPoints(achievementValue string) int {
	// 尝试从achievement_value中解析积分
	// 如果解析失败，返回默认值
	if achievementValue == "" {
		return 0
	}

	// 根据里程碑类型返回不同的积分
	// 这里可以根据实际的achievement_value格式进行解析
	// 暂时根据成就类型返回不同积分
	switch achievementValue {
	case "first_checkin":
		return 20 // 首次打卡
	case "streak_7":
		return 50 // 连续7天
	case "streak_30":
		return 200 // 连续30天
	case "camp_complete":
		return 100 // 完成训练营
	case "medal_earned":
		return 30 // 获得勋章
	default:
		return 10 // 默认积分
	}
}

// getTodayStatus 获取今日状态
func (s *growthSystemService) getTodayStatus(childID uint, activeCamps int, pendingContracts int) (*TodayStatusInfo, error) {
	// 检查今日是否已打卡（这里简化处理，检查第一个训练营）
	hasCheckedIn := false
	if activeCamps > 0 && s.checkinService != nil {
		// TODO: 实现更精确的今日打卡状态检查
		todayStatus, err := s.checkinService.GetTodayCheckinStatus(childID, 1) // 假设检查第一个训练营
		if err == nil {
			hasCheckedIn = todayStatus.HasCheckedIn
		}
	}

	// 计算今日积分（简化处理）
	todayPoints := 0
	if hasCheckedIn {
		todayPoints = 20 // 假设打卡获得20积分
	}

	// 生成推荐行动
	var recommendations []string
	if !hasCheckedIn && activeCamps > 0 {
		recommendations = append(recommendations, "完成今日训练打卡")
	}
	if pendingContracts > 0 {
		recommendations = append(recommendations, "查看进行中的家庭契约")
	}
	if len(recommendations) == 0 {
		recommendations = append(recommendations, "保持良好的训练习惯")
	}

	return &TodayStatusInfo{
		HasCheckedIn:     hasCheckedIn,
		ActiveCamps:      activeCamps,
		PendingContracts: pendingContracts,
		TodayPoints:      todayPoints,
		Recommendations:  recommendations,
	}, nil
}

// getMedalsData 获取勋章数据
func (s *growthSystemService) getMedalsData(childID uint) ([]*MedalInfo, error) {
	// 1. 获取所有活跃的系统勋章定义
	allMedals, _, err := s.medalsRepo.List(0, 100) // 获取前100个勋章
	if err != nil {
		logger.Error("Failed to get all medals", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取勋章定义失败")
	}

	// 过滤出活跃的勋章
	activeMedals := make([]*models.Medals, 0)
	for _, medal := range allMedals {
		if medal.IsActive == 1 {
			activeMedals = append(activeMedals, medal)
		}
	}

	// TODO: 重构为基于participation_id的勋章获取
	// 暂时返回空列表以便编译通过
	var childMedals []*models.ChildMedals

	// 创建孩子勋章记录的映射，便于查找
	childMedalMap := make(map[uint]*models.ChildMedals)
	if childMedals != nil {
		for _, childMedal := range childMedals {
			if childMedal.ChildID == int64(childID) {
				childMedalMap[uint(childMedal.MedalID)] = childMedal
			}
		}
	}

	// 3. 组合数据，生成勋章信息列表
	medalInfos := make([]*MedalInfo, 0, len(activeMedals))
	for _, medal := range activeMedals {
		medalInfo := &MedalInfo{
			ID:                   medal.ID,
			Name:                 medal.Name,
			Description:          medal.Description,
			Icon:                 medal.Icon,
			Level:                int(medal.MedalLevel),
			LevelName:            s.getMedalLevelName(int(medal.MedalLevel)),
			ConditionType:        int(medal.ConditionType),
			ConditionDescription: medal.ConditionDescription,
			PointsReward:         medal.PointsReward,
			IsUnlocked:           false,
			Progress:             0,
			Target:               medal.ConditionValue,
			UnlockedAt:           "",
			IsContractMedal:      s.isContractMedal(int(medal.ConditionType)),
			SortOrder:            medal.SortOrder,
		}

		// 检查孩子是否已获得此勋章
		if childMedal, exists := childMedalMap[medal.ID]; exists {
			medalInfo.IsUnlocked = childMedal.IsUnlocked == 1
			medalInfo.Progress = childMedal.CurrentProgress
			if !childMedal.UnlockedAt.IsZero() {
				medalInfo.UnlockedAt = childMedal.UnlockedAt.Format("2006-01-02")
			}
		}

		medalInfos = append(medalInfos, medalInfo)
	}

	logger.Info("Medals data retrieved successfully", "child_id", childID, "total_medals", len(medalInfos))
	return medalInfos, nil
}

// getMedalLevelName 获取勋章等级名称
func (s *growthSystemService) getMedalLevelName(level int) string {
	switch level {
	case 1:
		return "铜牌"
	case 2:
		return "银牌"
	case 3:
		return "金牌"
	case 4:
		return "钻石"
	default:
		return "普通"
	}
}

// isContractMedal 判断是否为契约勋章
func (s *growthSystemService) isContractMedal(conditionType int) bool {
	// 根据条件类型判断是否为契约相关勋章
	// 这里可以根据业务需求调整判断逻辑
	// 目前简单判断：条件类型为4或以上的认为是契约勋章
	return conditionType >= 4
}

// recordGrowthTrack 记录成长轨迹
func (s *growthSystemService) recordGrowthTrack(childID uint, campID uint, checkinID uint) error {
	// 获取参与记录ID
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
	if err != nil {
		logger.Error("Failed to get participation for growth track", "child_id", childID, "camp_id", campID, "error", err)
		return errcode.ErrDatabase.WithDetails("获取参与记录失败")
	}

	// 创建成长轨迹记录
	growthTrack := &models.GrowthTracks{
		ChildID:              int64(childID),
		ParticipationID:      int64(participation.ID),
		MilestoneType:        1, // 1: 首次打卡
		MilestoneTitle:       "完成训练营打卡",
		MilestoneDescription: "完成训练营打卡，获得积分奖励",
		MilestoneIcon:        "checkin",
		RelatedID:            int64(checkinID),
		RelatedType:          "checkin",
		AchievementValue:     "1",
		AchievementDetails:   "",
		IsShareable:          1,
	}

	// 保存成长轨迹记录
	err = s.growthTracksRepo.Create(growthTrack)
	if err != nil {
		logger.Error("Failed to create growth track record",
			"child_id", childID,
			"camp_id", campID,
			"checkin_id", checkinID,
			"error", err)
		return errcode.ErrDatabase.WithDetails("创建成长轨迹记录失败")
	}

	logger.Info("Growth track recorded successfully",
		"child_id", childID,
		"camp_id", campID,
		"checkin_id", checkinID,
		"track_id", growthTrack.ID)
	return nil
}

// checkAndUnlockMedals 检查并解锁勋章
func (s *growthSystemService) checkAndUnlockMedals(childID uint) error {
	// 获取所有勋章（使用 List 方法获取前100个勋章）
	medals, _, err := s.medalsRepo.List(0, 100)
	if err != nil {
		logger.Error("Failed to get medals", "child_id", childID, "error", err)
		return errcode.ErrDatabase.WithDetails("获取勋章列表失败")
	}

	// TODO: 重构为基于participation_id的勋章获取
	// 暂时返回空列表以便编译通过
	var childMedals []*models.ChildMedals

	// 创建已获得勋章的映射（只包含当前孩子的勋章）
	earnedMedalsMap := make(map[uint]bool)
	for _, childMedal := range childMedals {
		if uint(childMedal.ChildID) == childID {
			earnedMedalsMap[uint(childMedal.MedalID)] = true
		}
	}

	// 检查每个勋章的解锁条件
	for _, medal := range medals {
		// 跳过已获得的勋章
		if earnedMedalsMap[medal.ID] {
			continue
		}

		// 检查勋章解锁条件
		unlocked, err := s.checkMedalCondition(childID, medal)
		if err != nil {
			logger.Error("Failed to check medal condition",
				"child_id", childID,
				"medal_id", medal.ID,
				"error", err)
			continue
		}

		// 如果满足条件，解锁勋章
		if unlocked {
			err = s.unlockMedal(childID, medal)
			if err != nil {
				logger.Error("Failed to unlock medal",
					"child_id", childID,
					"medal_id", medal.ID,
					"error", err)
				continue
			}
			logger.Info("Medal unlocked successfully",
				"child_id", childID,
				"medal_id", medal.ID,
				"medal_name", medal.Name)
		}
	}

	return nil
}

// checkMedalCondition 检查勋章解锁条件
func (s *growthSystemService) checkMedalCondition(childID uint, medal *models.Medals) (bool, error) {
	// TODO: 重构为基于participation_id的勋章条件检查
	// 暂时返回false以便编译通过
	logger.Warn("Medal condition check temporarily disabled during refactoring",
		"medal_id", medal.ID,
		"condition_type", medal.ConditionType,
		"child_id", childID)
	return false, nil
}

// unlockMedal 解锁勋章
func (s *growthSystemService) unlockMedal(childID uint, medal *models.Medals) error {
	// 创建用户勋章记录
	childMedal := &models.ChildMedals{
		ChildID:         int64(childID),
		MedalID:         int(medal.ID),
		IsUnlocked:      1, // 已解锁
		CurrentProgress: medal.ConditionValue,
		TargetProgress:  medal.ConditionValue,
		UnlockedAt:      time.Now(),
		PointsEarned:    medal.PointsReward,
	}

	err := s.childMedalsRepo.Create(childMedal)
	if err != nil {
		return errcode.ErrDatabase.WithDetails("创建用户勋章记录失败")
	}

	// 记录成长轨迹
	// 获取参与记录ID（这里简化处理，设为0）
	growthTrack := &models.GrowthTracks{
		ChildID:              int64(childID),
		ParticipationID:      0, // 勋章解锁不关联特定参与记录
		MilestoneType:        4, // 4: 获得勋章
		MilestoneTitle:       "获得新勋章",
		MilestoneDescription: fmt.Sprintf("解锁勋章：%s", medal.Name),
		MilestoneIcon:        medal.Icon,
		RelatedID:            int64(medal.ID),
		RelatedType:          "medal",
		AchievementValue:     fmt.Sprintf("%d", medal.PointsReward),
		AchievementDetails:   "",
		IsShareable:          1,
	}

	err = s.growthTracksRepo.Create(growthTrack)
	if err != nil {
		logger.Error("Failed to record medal unlock in growth track",
			"child_id", childID,
			"medal_id", medal.ID,
			"error", err)
		// 成长轨迹记录失败不影响勋章解锁
	}

	return nil
}
